<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { settingApi, type Settings } from '../services/settingApi';
import moment from 'moment-timezone';

const settings = ref<Settings>({
    feishu: {
        app_id: '',
        app_secret: '',
        verification_token: '',
        encrypt_key: '',
        receive_id: ''
    },
    emailMonitor: []
});

const loading = ref(false);

onMounted(async () => {
    try {
        const data = await settingApi.get();
        // 合并默认值和获取的数据
        settings.value = {
            feishu: {
                ...settings.value.feishu,
                ...data.feishu
            },
            emailMonitor: data.emailMonitor || []
        };
    } catch (error) {
        MessagePlugin.error('获取设置失败');
    }
});

const handleSave = async () => {
    loading.value = true;
    try {
        if(!settings.value)
        {
            return
        }
        await settingApi.update(settings.value);
        MessagePlugin.success('保存成功');
    } catch (error) {
        MessagePlugin.error('保存失败');
    } finally {
        loading.value = false;
    }
};

// 添加新的监控邮箱
const addEmailMonitor = () => {
    if (!settings.value.emailMonitor) {
        settings.value.emailMonitor = [];
    }
    // 初始化为上海时区的当前时间
    // 使用moment-timezone获取上海时区的当前时间
    const shanghaiTime = moment().tz('Asia/Shanghai');

    settings.value.emailMonitor.push({
        email: '',
        lastEmailTimestamp: shanghaiTime.format('YYYY-MM-DD HH:mm:ss'),
        checkInterval: 60
    });
};

// 删除监控邮箱
const removeEmailMonitor = (index: number) => {
    if (settings.value.emailMonitor) {
        settings.value.emailMonitor.splice(index, 1);
    }
};


</script>

<template>
    <div class="setting-container p-2 md:p-5">

        <t-form :data="settings" @submit="handleSave">
            <t-card bordered>
                <t-divider>飞书配置</t-divider>
                <t-form-item label="应用ID" name="feishu.app_id">
                    <t-input v-model="settings.feishu.app_id" placeholder="请输入飞书应用ID" />
                </t-form-item>
                <t-form-item label="应用密钥" name="feishu.app_secret">
                    <t-input v-model="settings.feishu.app_secret" type="password" placeholder="请输入飞书应用密钥" />
                </t-form-item>
                <t-form-item label="验证Token" name="feishu.verification_token">
                    <t-input v-model="settings.feishu.verification_token" placeholder="请输入飞书应用验证Token" />
                </t-form-item>
                <t-form-item label="加密Key" name="feishu.encrypt_key">
                    <t-input v-model="settings.feishu.encrypt_key" placeholder="请输入飞书应用加密Key" />
                </t-form-item>
                <t-form-item label="接收ID" name="feishu.receive_id">
                    <t-input v-model="settings.feishu.receive_id" placeholder="请输入飞书机器人接收ID" />
                </t-form-item>

                <t-divider>监控邮箱配置</t-divider>
                <div class="mb-4 flex justify-end">
                    <t-button theme="default" @click="addEmailMonitor">
                        <template #icon><t-icon name="add" /></template>
                        添加监控邮箱
                    </t-button>
                </div>

                <div v-if="settings.emailMonitor && settings.emailMonitor.length > 0" class="mb-4">
                    <!-- 表头 -->
                    <div class="grid grid-cols-4 gap-4 mb-2 font-bold">
                        <div>邮箱地址</div>
                        <div>检查间隔(分钟)</div>
                        <div>邮件时间</div>
                        <div>操作</div>
                    </div>

                    <!-- 每一行邮箱配置 -->
                    <div v-for="(item, index) in settings.emailMonitor" :key="index" class="grid grid-cols-4 gap-4 mb-3 items-center">
                        <div>
                            <t-input v-model="item.email" placeholder="请输入邮箱地址" />
                        </div>
                        <div>
                            <t-input-number v-model="item.checkInterval" :min="1" :max="1440" placeholder="请输入检查间隔" />
                        </div>
                        <div>
                            <t-date-picker
                                v-model="item.lastEmailTimestamp"
                                enable-time-picker
                                clearable
                                placeholder="请选择上次邮件时间"
                                format="YYYY-MM-DD HH:mm:ss"
                                :timezone="{ name: 'Asia/Shanghai', offset: 8 }"
                            />
                        </div>
                        <div>
                            <t-button theme="danger" variant="text" @click="removeEmailMonitor(index)">
                                <template #icon><t-icon name="delete" /></template>
                                删除
                            </t-button>
                        </div>
                    </div>
                </div>

                <t-form-item class="flex justify-center">
                    <t-button theme="primary" type="submit" :loading="loading">保存设置</t-button>
                </t-form-item>
            </t-card>
        </t-form>
    </div>
</template>

<style scoped>

</style>
