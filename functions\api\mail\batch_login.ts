import { authApiToken, authMiddleware } from "../../utils/auth.js";
import { addCorsHeaders } from "../../utils/cors.js";
import { AuthService } from "../../utils/authService.js";

// 并发处理函数
async function processBatch<T>(
    items: T[],
    processor: (item: T) => Promise<any>,
    concurrency: number = 2
): Promise<any[]> {
    // 创建一个队列来存储所有的 promise
    const queue: Promise<any>[] = [];
    const results: any[] = new Array(items.length);
    let nextIndex = 0;

    // 创建指定数量的worker
    const workers = new Array(concurrency).fill(null).map(async () => {
        while (nextIndex < items.length) {
            const currentIndex = nextIndex++;
            try {
                const result = await processor(items[currentIndex]);
                results[currentIndex] = result;
            } catch (error) {
                results[currentIndex] = {
                    error: error instanceof Error ? error.message : 'Unknown error'
                };
            }
        }
    });

    // 等待所有worker完成
    await Promise.all(workers);
    return results;
}

export const onRequest = async (context: RouteContext): Promise<Response> => {
    const request = context.request;
    const env: Env = context.env;

    const authResponse = await authMiddleware(request, env);
    const apiResponse = await authApiToken(request, env);
    if (authResponse && apiResponse) {
        return addCorsHeaders(authResponse);
    }

    try {
        const { emails } = await request.json() as { emails: string[] };
        if (!emails || !Array.isArray(emails)) {
            throw new Error("Emails array is required");
        }

        const authService = new AuthService(env);
        const results = await processBatch(
            emails,
            async (email) => {
                try {
                    // 执行邮箱登录
                    const result = await authService.loginMail(email);
                    return {
                        email,
                        success: result.success,
                        message: result.success ? "邮箱登录成功" : "邮箱登录失败",
                        error: result.error
                    };
                } catch (error: any) {
                    return {
                        email,
                        success: false,
                        error: error.message || 'Failed to login'
                    };
                }
            },
            2
        );

        return new Response(
            JSON.stringify(results),
            {
                status: 200,
                headers: { 
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                }
            }
        );

    } catch (error: any) {
        return new Response(
            JSON.stringify({ error: error.message }),
            { 
                status: 500,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                }
            }
        );
    }
};
