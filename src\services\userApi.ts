import { API_BASE_URL,  handleResponse } from './util';

export interface LoginResponse {
    success: boolean;
    token: string;
    message?: string;
}

export const userApi = {
    async login(username: string, password: string): Promise<LoginResponse> {
        const response = await fetch(`${API_BASE_URL}/api/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password }),
        });
        return handleResponse(response);
    }
};


