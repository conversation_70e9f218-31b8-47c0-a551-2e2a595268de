import { API_BASE_URL, getHeaders, handleResponse } from './util';

export const mailApi = {
    async getAuthStatus(email: string) {
        const response = await fetch(
            `${API_BASE_URL}/api/mail/status`,
            {
                headers: getHeaders(),
                method: 'POST',
                body: JSON.stringify({ email })
            }
        );
        return handleResponse(response);
    },

    // Add new batch refresh methods
    async refreshAuthBatch(emails: string[]) {
        const response = await fetch(
            `${API_BASE_URL}/api/mail/batch`,
            {
                headers: getHeaders(),
                method: 'POST',
                body: JSON.stringify({ emails })
            }
        );
        return handleResponse(response);
    },

    async refreshAuth(email: string) {
        const response = await fetch(
            `${API_BASE_URL}/api/mail/auth`,
            {
                headers: getHeaders(),
                method: 'POST',
                body: JSON.stringify({ email })
            }
        );
        return handleResponse(response);
    },

    async activateMailbox(email: string) {
        const response = await fetch(
            `${API_BASE_URL}/api/mail/activate`,
            {
                headers: getHeaders(),
                method: 'POST',
                body: JSON.stringify({ email })
            }
        );
        return handleResponse(response);
    },

    async getLatestMails(email: string) {
        const response = await fetch(
            `${API_BASE_URL}/api/mail/new`,
            {
                headers: getHeaders(),
                method: 'POST',
                body: JSON.stringify({ email })
            }
        );
        return handleResponse(response);
    },

    async getAllMails(email: string) {
        const response = await fetch(
            `${API_BASE_URL}/api/mail/all`,
            {
                headers: getHeaders(),
                method: 'POST',
                body: JSON.stringify({ email })
            }
        );
        return handleResponse(response);
    },

    async sendMail(params: {
        email: string;
        to: string[];
        subject: string;
        body: string;
        isHtml?: boolean;
    }) {
        const response = await fetch(
            `${API_BASE_URL}/api/mail/send`,
            {
                headers: getHeaders(),
                method: 'POST',
                body: JSON.stringify(params)
            }
        );
        return handleResponse(response);
    },

    async loginMail(email: string) {
        const response = await fetch(
            `${API_BASE_URL}/api/mail/login`,
            {
                headers: getHeaders(),
                method: 'POST',
                body: JSON.stringify({ email })
            }
        );
        return handleResponse(response);
    }
};
