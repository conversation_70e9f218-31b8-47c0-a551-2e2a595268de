import { API_BASE_URL, getHeaders, handleResponse } from './util';

export interface Account {
    email: string;
    password: string;
    proofEmail: string;
}

export const accountApi = {
    async post(accounts: Account[]) {
        const response = await fetch(
            `${API_BASE_URL}/api/account`,
            {
                headers: getHeaders(),
                method: 'POST',
                body: JSON.stringify(accounts)
            }
        );
        return handleResponse(response);
    },

    async get(): Promise<Account[]> {
        const response = await fetch(
            `${API_BASE_URL}/api/account`,
            {
                headers: getHeaders()
            }
        );
        return handleResponse(response);
    },

}
