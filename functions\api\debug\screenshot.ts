import { CORS_HEADERS as corsHeaders } from '../../utils/cors.js';
import { getScreenshot } from '../../utils/debugStorage.js';

export async function onRequestGET(context: any) {
    const { request } = context;
    const url = new URL(request.url);
    const debugId = url.searchParams.get('id');

    if (!debugId) {
        return new Response(JSON.stringify({ error: 'Missing debug ID' }), {
            status: 400,
            headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
            }
        });
    }

    try {
        // 获取截图数据
        const screenshotBuffer = await getScreenshot(debugId);

        if (!screenshotBuffer) {
            return new Response(JSON.stringify({ error: 'Screenshot not found' }), {
                status: 404,
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                }
            });
        }

        return new Response(screenshotBuffer, {
            headers: {
                'Content-Type': 'image/png',
                'Cache-Control': 'public, max-age=3600',
                ...corsHeaders
            }
        });

    } catch (error) {
        console.error('Error retrieving screenshot:', error);
        return new Response(JSON.stringify({
            error: 'Failed to retrieve screenshot',
            details: error instanceof Error ? error.message : String(error)
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
            }
        });
    }
}

export async function onRequestOPTIONS() {
    return new Response(null, {
        status: 200,
        headers: corsHeaders
    });
}
