import router from "../router";

export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';

export function getHeaders() {
    return {
        'Content-Type': 'application/json',
        "x-app-token": `Bearer ${localStorage.getItem('token')}`,
        "Authorization": `Bearer ${localStorage.getItem('token')}`
    };
}
export async function handleResponse(response: Response) {
    if (response.status === 401) {
        localStorage.removeItem('isAuthenticated');
        localStorage.removeItem('token');
        router.push('/login');
        throw new Error('认证失败，请重新登录');
    }
    if (!response.ok) {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json();
            throw new Error(errorData.error || '请求失败');
        } else {
            const errorText = await response.text();
            throw new Error(errorText || '请求失败');
        }
    }
    return response.json();
}