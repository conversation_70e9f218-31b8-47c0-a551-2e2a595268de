import { CORS_HEADERS as corsHeaders } from '../../utils/cors.js';
import { getAllDebugInfo } from '../../utils/debugStorage.js';

export async function onRequestGET(context: any) {
    const { request } = context;
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '50');

    try {
        // 获取所有调试信息
        const debugItems = await getAllDebugInfo();

        // 限制返回数量
        const limitedItems = debugItems.slice(0, limit);

        return new Response(JSON.stringify({
            success: true,
            data: limitedItems,
            total: debugItems.length
        }), {
            headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
            }
        });

    } catch (error) {
        console.error('Error retrieving debug list:', error);
        return new Response(JSON.stringify({
            error: 'Failed to retrieve debug list',
            details: error instanceof Error ? error.message : String(error)
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
            }
        });
    }
}

export async function onRequestOPTIONS() {
    return new Response(null, {
        status: 200,
        headers: corsHeaders
    });
}
