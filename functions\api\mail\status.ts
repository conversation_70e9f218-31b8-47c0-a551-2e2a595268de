import { authApiToken, authMiddleware } from "../../utils/auth.js";
import { addCorsHeaders } from "../../utils/cors.js";

export const onRequest = async (context: RouteContext): Promise<Response> => {
    const request = context.request;
    const env: Env = context.env;

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    const apiResponse = await authApiToken(request, env);
    if (authResponse && apiResponse) {
        return addCorsHeaders(authResponse);
    }

    try {
        const { email } = await request.json() as any;
        if (!email) {
            throw new Error("Email is required");
        }

        // 获取刷新令牌信息
        const tokenInfoStr = await env.KV.get(`refresh_token_${email}`);
        if (!tokenInfoStr) {
            return new Response(
                JSON.stringify({ 
                    authenticated: false,
                    message: "未认证"
                }),
                { 
                    status: 200,
                    headers: {
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*'
                    }
                }
            );
        }

        const tokenInfo = JSON.parse(tokenInfoStr);


        return new Response(
            JSON.stringify({
                authenticated: true,
                message: "已认证",
                timestamp: tokenInfo.timestamp
            }),
            {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                }
            }
        );

    } catch (error: any) {
        return new Response(
            JSON.stringify({ error: error.message }),
            { 
                status: 500,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                }
            }
        );
    }
};