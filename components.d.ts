/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    MonacoEditor: typeof import('./src/components/MonacoEditor.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TAlert: typeof import('tdesign-vue-next')['Alert']
    TAside: typeof import('tdesign-vue-next')['Aside']
    TButton: typeof import('tdesign-vue-next')['Button']
    TCard: typeof import('tdesign-vue-next')['Card']
    TCheckbox: typeof import('tdesign-vue-next')['Checkbox']
    TContent: typeof import('tdesign-vue-next')['Content']
    TDatePicker: typeof import('tdesign-vue-next')['DatePicker']
    TDialog: typeof import('tdesign-vue-next')['Dialog']
    TDivider: typeof import('tdesign-vue-next')['Divider']
    TDrawer: typeof import('tdesign-vue-next')['Drawer']
    TDropdown: typeof import('tdesign-vue-next')['Dropdown']
    TFooter: typeof import('tdesign-vue-next')['Footer']
    TForm: typeof import('tdesign-vue-next')['Form']
    TFormItem: typeof import('tdesign-vue-next')['FormItem']
    THeader: typeof import('tdesign-vue-next')['Header']
    TIcon: typeof import('tdesign-vue-next')['Icon']
    TInput: typeof import('tdesign-vue-next')['Input']
    TInputNumber: typeof import('tdesign-vue-next')['InputNumber']
    TLayout: typeof import('tdesign-vue-next')['Layout']
    TList: typeof import('tdesign-vue-next')['List']
    TListItem: typeof import('tdesign-vue-next')['ListItem']
    TLoading: typeof import('tdesign-vue-next')['Loading']
    TMenu: typeof import('tdesign-vue-next')['Menu']
    TMenuItem: typeof import('tdesign-vue-next')['MenuItem']
    TPagination: typeof import('tdesign-vue-next')['Pagination']
    TTable: typeof import('tdesign-vue-next')['Table']
    TTag: typeof import('tdesign-vue-next')['Tag']
    TTbody: typeof import('tdesign-vue-next')['Tbody']
    TTd: typeof import('tdesign-vue-next')['Td']
    TTextarea: typeof import('tdesign-vue-next')['Textarea']
    TTh: typeof import('tdesign-vue-next')['Th']
    TThead: typeof import('tdesign-vue-next')['Thead']
    TTimePicker: typeof import('tdesign-vue-next')['TimePicker']
    TTr: typeof import('tdesign-vue-next')['Tr']
  }
}
