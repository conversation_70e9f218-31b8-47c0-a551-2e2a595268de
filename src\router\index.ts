import { createRouter, createWebHistory } from 'vue-router'


const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: [
        {
            path: '/',
            redirect: '/mail',
            meta: { requiresAuth: true }
        },
        {
            path: '/login',
            name: 'Login',
            component: () => import('../views/LoginView.vue'),
            meta: { requiresAuth: false }
        },
        {
            path: '/mail',
            name: 'Mail',
            component: () => import('../views/MailView.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/setting',
            name: 'Setting',
            component: () => import('../views/SettingView.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/account',
            name: 'Account',
            component: () => import('../views/AccountView.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/debug',
            name: 'Debug',
            component: () => import('../views/DebugView.vue'),
            meta: { requiresAuth: true }
        },
    ],
})
// 添加路由守卫
router.beforeEach((to, from, next) => {
    const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true'
    if (to.meta.requiresAuth && !isAuthenticated) {
        next('/login')
    } else if (to.path === '/login' && isAuthenticated) {
        next('/')
    } else {
        next()
    }
})

export default router
