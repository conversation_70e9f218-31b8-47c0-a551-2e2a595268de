import { API_BASE_URL, getHeaders, handleResponse } from './util';

export interface ServerInfo {
  clientIP: string;
  serverIP: string;
  hostname: string;
  serverTime: string;
}

export const ipApi = {
  async getServerInfo(): Promise<ServerInfo> {
    const response = await fetch(
      `${API_BASE_URL}/api/ip`,
      {
        headers: getHeaders()
      }
    );
    const result = await handleResponse(response);
    return result.data;
  }
};
