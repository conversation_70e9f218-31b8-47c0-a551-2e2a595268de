import { generateToken } from '../utils/jwt.js';

export const onRequest = async (context: RouteContext): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    try {
        // 解析登录凭证
        const credentials = await request.json() as LoginCredentials;
        // 验证用户名和密码
        if (credentials.username === env.USER_NAME && credentials.password === env.PASSWORD) {
            // 生成JWT令牌
            let token = await generateToken(credentials.username, env.JWT_SECRET);
            return new Response(
                JSON.stringify({
                    success: true,
                    token,
                    user: { username: credentials.username }
                }),
                {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        // 登录失败: 无效的凭证
        return new Response(
            JSON.stringify({
                success: false,
                error: 'Invalid credentials'
            }),
            {
                status: 401,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    } catch (error) {
        // 登录处理失败
        console.error(`登录处理失败:`, error);
        throw error;
    }
}