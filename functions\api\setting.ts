
import { authMiddleware } from "../utils/auth.js";
import { addCorsHeaders } from "../utils/cors.js";


export const onRequest = async (context: RouteContext): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;

    const authResponse = await authMiddleware(request, env);
    if (authResponse) {
        return addCorsHeaders(authResponse);
    }

    const KV_KEY = "settings"

    try {
        // GET 请求处理
        if (request.method === 'GET') {
            const settings = await env.KV.get(KV_KEY);
            return addCorsHeaders(new Response(settings || '{}', {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }));
        }

        // POST 请求处理
        if (request.method === 'POST') {
            const data = await request.json();
            // 存储账号数据
            await env.KV.put(KV_KEY, JSON.stringify(data));

            return addCorsHeaders(new Response(JSON.stringify({ message: '保存成功' }), {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }));
        }

        // 不支持的请求方法
        return addCorsHeaders(new Response(JSON.stringify({ error: '不支持的请求方法' }), {
            status: 405,
            headers: { 'Content-Type': 'application/json' }
        }));

    } catch (error) {
        return addCorsHeaders(new Response(JSON.stringify({ error: '服务器内部错误' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
};