# MSMail API

这是一个基于 Microsoft Graph API 的邮箱服务接口，可以读取和发送 Microsoft 365 / Outlook 邮件。

## 功能特性

- ✨ 支持 Microsoft 账号登录认证

- 📧 读取邮箱所有邮件

- 📨 获取最新邮件

- 📤 发送新邮件

- ⚙️ 邮箱设置管理

## 安装部署

1. 克隆项目

```bash
git clone <repository-url>
cd msmail
```

1. 安装依赖

```bash
npm install
```

1. 配置环境变量 创建 `.env` 文件：

```
PORT=8788
CLOUDFLARE_ACCOUNT_ID=your_account_id
CLOUDFLARE_NAMESPACE_ID=your_namespace_id
CLOUDFLARE_API_TOKEN=your_api_token
JWT_SECRET = ""
USER_NAME = ""
PASSWORD = ""
API_TOKEN = ""
AUTH_REDIRECT_URI= # 需要在entra.microsoft.com的身份验证中添加
ENTRA_CLIENT_ID=""
ENTRA_CLIENT_SECRET=""
```

1. 运行项目

```bash
npm run dev
```

## API 接口说明

### 认证相关

- `GET /api/login` - 登录接口

- `GET /api/account` - 获取账号信息

### 邮件操作

- `GET /api/mail/auth` - 获取邮箱授权

- `GET /api/mail/callback` - 授权回调接口

- `GET /api/mail/all` - 获取所有邮件

- `GET /api/mail/new` - 获取最新邮件

- `POST /api/mail/send` - 发送邮件

### 设置管理

- `GET /api/setting` - 获取/更新邮箱设置

## 技术栈

- Hono.js - Web 框架

- Microsoft Graph API - 邮箱服务

- Cloudflare KV - 数据存储

- Node.js - 运行环境

## 许可证

MIT