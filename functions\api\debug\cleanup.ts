import { CORS_HEADERS as corsHeaders } from '../../utils/cors.js';
import { cleanupOldDebugFiles } from '../../utils/debugStorage.js';

export async function onRequestPOST(context: any) {
    const { request } = context;
    const url = new URL(request.url);
    const daysToKeep = parseInt(url.searchParams.get('days') || '7');

    try {
        const deletedCount = await cleanupOldDebugFiles(daysToKeep);

        return new Response(JSON.stringify({
            success: true,
            message: `已清理 ${deletedCount} 条超过 ${daysToKeep} 天的调试数据`,
            deletedCount
        }), {
            headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
            }
        });

    } catch (error) {
        console.error('Error cleaning up debug data:', error);
        return new Response(JSON.stringify({
            error: 'Failed to cleanup debug data',
            details: error instanceof Error ? error.message : String(error)
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
            }
        });
    }
}

export async function onRequestOPTIONS() {
    return new Response(null, {
        status: 200,
        headers: corsHeaders
    });
}
