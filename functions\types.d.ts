
interface KVNamespace {
  put: (key: string, value: string, options?: { expiration?: number, expirationTtl?: number, metadata?: object }) => Promise<void>;
  get: (key: string) => Promise<string | null>;
  delete: (key: string) => Promise<void>;
  list: (options?: { prefix?: string }) => Promise<{ keys: { name: string }[] }>
}

interface Env {
  API_TOKEN: string;    // API 访问令牌
  JWT_SECRET: string;   // JWT 密钥
  USER_NAME: string;    // 用户名
  PASSWORD: string;     // 密码
  ENTRA_CLIENT_ID: string;
  ENTRA_CLIENT_SECRET: string;
  AUTH_REDIRECT_URI: string;
  PROOF_GODGODGAME_TOKEN: string;
  PROOF_IGIVEN_TOKEN: string;
  KV: KVNamespace;
  ASSETS: any;
}

/**
 * 登录凭证接口
 */
interface LoginCredentials {
  /** 用户名 */
  username: string;
  /** 密码 */
  password: string;
}


interface RouteContext {
  request: Request;
  functionPath: string;
  waitUntil: (promise: Promise<any>) => void;
  passThroughOnException: () => void;
  next: (input?: Request | string, init?: RequestInit) => Promise<Response>;
  env: Env;
  params: any;
  data: any;
}
