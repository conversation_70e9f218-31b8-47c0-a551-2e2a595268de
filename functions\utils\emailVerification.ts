export async function getVerificationCode(proofApi: string, apiKey: String, proofEmail: string, timestamp: number): Promise<string> {
    const maxRetries = 30;
    timestamp = timestamp - 5;
    console.log(`开始获取验证码${proofApi},${apiKey},${timestamp},${new Date(timestamp * 1000)}`);
    for (let i = 0; i < maxRetries; i++) {
        try {
            const params = new URLSearchParams({
                to: proofEmail,
                from: '<EMAIL>',
                timestamp: timestamp.toString()
            });

            const url = `${proofApi}?${params.toString()}`;
            const response = await fetch(url, {
                headers: {
                    'Authorization': `Bearer ${apiKey}`
                },
                method: 'GET'
            });

            if (response.status === 200) {
                const data: any = await response.json();
                const match = data.text.match(/:\s*(\d+)\n\n/);
                if (match) {
                    console.log(proofEmail, `获取验证码成功: ${match[1]}`);
                    return match[1];
                }
            }
            else {
                console.log(proofEmail, `获取验证码失败: ${await response.text()}`);
            }
            await new Promise(resolve => setTimeout(resolve, 2000));
        } catch (error:any) {
            console.error(proofEmail, `获取验证码出错: ${error.message}`);
            if (i === maxRetries - 1) {
                throw new Error("Failed to get verification code after maximum retries");
            }
            // 错误后等待3秒再重试
            await new Promise(resolve => setTimeout(resolve, 3000));
            continue;
        }
    }
    throw new Error("Failed to get verification code");
}