
export const onRequest = async (context: RouteContext): Promise<Response> => {
    const request = context.request;
    const env: Env = context.env;

    try {
        const url = new URL(request.url);
        const code = url.searchParams.get('code');
        const email = url.searchParams.get('state'); // 假设我们在state参数中传递了email
        const error_Msg = url.searchParams.get('error_description');
        if (!code || !email) {
            throw new Error(`Missing code or email:${error_Msg}`);
        }
        console.log(code,email)
        // 将授权码存储到KV中
        await env.KV.put(`code_${email}`, code, { expirationTtl: 300 }); // 5分钟过期

        return new Response("Authorization successful. You can close this window.", {
            status: 200,
            headers: { 'Content-Type': 'text/plain' }
        });

    } catch (error: any) {
        return new Response(
            JSON.stringify({ error: error.message }),
            { status: 500 }
        );
    }
};