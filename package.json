{"name": "msmail", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host --port 5010", "dev:server": "cross-env NODE_ENV=development tsx watch --no-cache index.ts", "build": "vue-tsc -b && vite build", "build:server": "npm run build && tsc -p ./tsconfig.functions.json", "test": "vitest"}, "dependencies": {"@hono/node-server": "^1.13.8", "@monaco-editor/loader": "^1.5.0", "@tailwindcss/vite": "^4.0.14", "dotenv": "^16.4.7", "hono": "^4.7.4", "moment-timezone": "^0.5.48", "monaco-editor": "^0.52.2", "pinia": "^3.0.1", "playwright": "^1.51.0", "tailwindcss": "^4.0.14", "tdesign-vue-next": "^1.11.4", "unstorage": "^1.15.0", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@types/node": "^22.10.2", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "tsx": "^4.7.1", "typescript": "^5.5.2", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^28.0.0", "vite": "^6.2.0", "vitest": "~3.0.7", "vue-tsc": "^2.2.4"}}