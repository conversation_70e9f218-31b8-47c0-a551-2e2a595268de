import { API_BASE_URL, getHeaders, handleResponse } from './util';

export interface EmailMonitorConfig {
    /** 监控的邮箱地址 */
    email: string;
    /** 上次邮件时间戳 (用于判断新邮件) */
    lastEmailTimestamp: string;
    /** 检查间隔 (分钟) */
    checkInterval: number;
    /** 上次检查时间 */
    lastCheckTime?: string;
    /** 上次邮件ID */
    lastEmailId?: string;
}

export interface Settings {
    /** 飞书配置 */
    feishu: {
        /** 飞书应用ID */
        app_id: string;
        /** 飞书应用密钥 */
        app_secret: string;
        /** 飞书应用验证Token */
        verification_token: string;
        /** 飞书应用加密Key */
        encrypt_key: string;
        /** 飞书机器人接收ID */
        receive_id: string;
    };
    /** 邮箱监控配置 */
    emailMonitor?: EmailMonitorConfig[];
}


export const settingApi = {
    async update(settings: Settings) {
        const response = await fetch(
            `${API_BASE_URL}/api/setting`,
            {
                headers: getHeaders(),
                method: 'POST',
                body: JSON.stringify(settings)
            }
        );
        return handleResponse(response);
    },

    async get(): Promise<Settings> {
        const response = await fetch(
            `${API_BASE_URL}/api/setting`,
            {
                headers: getHeaders()
            }
        );
        return handleResponse(response);
    },
}