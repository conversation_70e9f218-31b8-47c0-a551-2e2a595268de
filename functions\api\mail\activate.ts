
import { authApiToken, authMiddleware } from "../../utils/auth.js";
import { addCorsHeaders } from "../../utils/cors.js";
import { get_access_token, activateMailbox, syncMailbox } from '../../utils/mail.js';

export const onRequest = async (context: RouteContext): Promise<Response> => {
    const request = context.request;
    const env: Env = context.env;

    const authResponse = await authMiddleware(request, env);
    const apiResponse = await authApiToken(request, env);
    if (authResponse && apiResponse) {
        return addCorsHeaders(authResponse);
    }

    if (request.method === 'OPTIONS') {
        return new Response(null, {
            status: 200,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            }
        });
    }

    if (request.method !== 'POST') {
        return new Response('Method not allowed', { status: 405 });
    }

    try {
        const { email } = await request.json() as any;
        if (!email) {
            throw new Error("Email is required");
        }

        // 从KV获取刷新令牌
        const tokenInfoStr = await env.KV.get(`refresh_token_${email}`);
        if (!tokenInfoStr) {
            throw new Error("No refresh token found for this email");
        }

        const tokenInfo = JSON.parse(tokenInfoStr);
        const access_token = await get_access_token(tokenInfo, env.ENTRA_CLIENT_ID, env.ENTRA_CLIENT_SECRET);

        // 执行邮箱激活操作
        console.log(`开始激活邮箱: ${email}`);
        await activateMailbox(access_token);
        await syncMailbox(access_token);

        // 等待同步完成
        await new Promise(resolve => setTimeout(resolve, 3000));

        return new Response(
            JSON.stringify({ 
                success: true, 
                message: "邮箱激活完成",
                email: email,
                timestamp: new Date().toISOString()
            }),
            {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                }
            }
        );
    } catch (error: any) {
        console.error('邮箱激活失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false,
                error: error.message,
                message: "邮箱激活失败"
            }),
            {
                status: 500,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                }
            }
        );
    }
};
